<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码生成器-猎户助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-image:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            z-index: -1;
        }

        .header-card {
            background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 50%, #C084FC 100%);
            border-radius: 24px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow:
                0 20px 40px rgba(139, 92, 246, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 24px;
            position: relative;
            overflow: hidden;
        }

        .header-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            pointer-events: none;
        }

        .app-title {
            color: white;
            font-size: 1.5em;
            font-weight: 600;
            margin: 0;
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(15px);
            box-shadow:
                0 4px 12px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow:
                0 8px 20px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 24px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 8px 32px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            padding: 32px;
            max-width: 500px;
            width: 100%;
            margin: 0 auto;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
        }

        h1 {
            color: #1f2937;
            margin-bottom: 24px;
            font-size: 1.8em;
            font-weight: 600;
            text-align: center;
        }

        .input-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
        }

        input[type="text"], textarea, select {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            outline: none;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            box-shadow:
                0 4px 12px rgba(0, 0, 0, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        input[type="text"]:focus, textarea:focus, select:focus {
            border-color: rgba(139, 92, 246, 0.6);
            background: rgba(255, 255, 255, 0.95);
            box-shadow:
                0 0 0 4px rgba(139, 92, 246, 0.1),
                0 8px 20px rgba(139, 92, 246, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            transform: translateY(-1px);
        }

        textarea {
            resize: vertical;
            min-height: 100px;
            font-family: inherit;
        }

        .size-group {
            display: flex;
            gap: 16px;
            align-items: end;
        }

        .size-group > div {
            flex: 1;
        }

        button {
            background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%);
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 8px 6px;
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
        }

        button:active {
            transform: translateY(0);
        }

        .qr-result {
            margin-top: 24px;
            padding: 28px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 24px;
            display: none;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(15px);
            box-shadow:
                0 12px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8),
                inset 0 -1px 0 rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .qr-result::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.02) 0%, rgba(168, 85, 247, 0.02) 100%);
            pointer-events: none;
        }

        .qr-result.show {
            display: block;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        #qrcode {
            margin: 20px 0;
            display: flex;
            justify-content: center;
        }

        #qrcode canvas {
            border-radius: 20px;
            box-shadow:
                0 12px 32px rgba(0, 0, 0, 0.15),
                0 4px 12px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 3px solid rgba(255, 255, 255, 0.8);
        }

        .download-btn {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
        }

        .download-btn:hover {
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
        }

        .clear-btn {
            background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
        }

        .clear-btn:hover {
            box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
        }

        .error {
            color: #EF4444;
            margin-top: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        @media (max-width: 480px) {
            .header-card {
                margin: 0 10px 20px 10px;
                padding: 20px;
            }

            .container {
                margin: 0 10px;
                padding: 24px;
            }

            .app-title {
                font-size: 1.3em;
            }

            h1 {
                font-size: 1.5em;
            }

            .size-group {
                flex-direction: column;
                gap: 12px;
            }

            button {
                width: 100%;
                margin: 6px 0;
            }
        }
    </style>
</head>
<body>
    <div class="header-card">
        <h2 class="app-title">二维码生成-猎户助手</h2>
        <button class="back-btn" onclick="goBack()">返回</button>
    </div>

    <div class="container">
        <h1>二维码生成器</h1>
        
        <div class="input-group">
            <label for="qrText">输入内容：</label>
            <textarea id="qrText" placeholder="请输入要生成二维码的内容（网址、文本等）"></textarea>
            <div class="error" id="textError"></div>
        </div>

        <div class="size-group">
            <div class="input-group">
                <label for="qrSize">二维码大小：</label>
                <select id="qrSize">
                    <option value="200">200x200</option>
                    <option value="300" selected>300x300</option>
                    <option value="400">400x400</option>
                    <option value="500">500x500</option>
                </select>
            </div>
            
            <div class="input-group">
                <label for="errorLevel">纠错级别：</label>
                <select id="errorLevel">
                    <option value="L">低 (L)</option>
                    <option value="M" selected>中 (M)</option>
                    <option value="Q">较高 (Q)</option>
                    <option value="H">高 (H)</option>
                </select>
            </div>
        </div>

        <button onclick="generateQR()">生成二维码</button>
        <button class="clear-btn" onclick="clearAll()">清空</button>

        <div class="qr-result" id="qrResult">
            <div id="qrcode"></div>
            <button class="download-btn" onclick="downloadQR()">下载二维码</button>
        </div>
    </div>

    <script src="https://unpkg.com/qrcode-generator@1.4.4/qrcode.js"></script>
    <script>
        let currentQRCode = null;

        function generateQR() {
            const text = document.getElementById('qrText').value.trim();
            const size = parseInt(document.getElementById('qrSize').value);
            const errorLevel = document.getElementById('errorLevel').value;
            const textError = document.getElementById('textError');
            const qrResult = document.getElementById('qrResult');
            const qrcodeDiv = document.getElementById('qrcode');

            // 清除之前的错误信息
            textError.textContent = '';

            // 验证输入
            if (!text) {
                textError.textContent = '请输入要生成二维码的内容';
                qrResult.classList.remove('show');
                return;
            }

            try {
                // 创建二维码对象
                const qr = qrcode(0, errorLevel);
                qr.addData(text);
                qr.make();

                // 清除之前的二维码
                qrcodeDiv.innerHTML = '';

                // 创建二维码图像
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const modules = qr.getModuleCount();
                const cellSize = size / modules;
                
                canvas.width = size;
                canvas.height = size;

                // 设置背景色为白色
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, size, size);

                // 绘制二维码
                ctx.fillStyle = '#000000';
                for (let row = 0; row < modules; row++) {
                    for (let col = 0; col < modules; col++) {
                        if (qr.isDark(row, col)) {
                            ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                        }
                    }
                }

                // 添加到页面
                qrcodeDiv.appendChild(canvas);
                currentQRCode = canvas;
                qrResult.classList.add('show');

            } catch (error) {
                textError.textContent = '生成二维码时出错，请检查输入内容';
                qrResult.classList.remove('show');
                console.error('QR Code generation error:', error);
            }
        }

        function downloadQR() {
            if (!currentQRCode) {
                alert('请先生成二维码');
                return;
            }

            try {
                // 创建下载链接
                const link = document.createElement('a');
                link.download = 'qrcode.png';
                link.href = currentQRCode.toDataURL('image/png');
                
                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                alert('下载失败，请重试');
                console.error('Download error:', error);
            }
        }

        function clearAll() {
            document.getElementById('qrText').value = '';
            document.getElementById('qrSize').value = '300';
            document.getElementById('errorLevel').value = 'M';
            document.getElementById('textError').textContent = '';
            document.getElementById('qrResult').classList.remove('show');
            document.getElementById('qrcode').innerHTML = '';
            currentQRCode = null;
        }

        // 回车键生成二维码
        document.getElementById('qrText').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                generateQR();
            }
        });

        // 返回上级目录
        function goBack() {
            window.location.href = '.././';
        }

        // 页面加载完成后聚焦到输入框
        window.addEventListener('load', function() {
            document.getElementById('qrText').focus();
        });
    </script>
</body>
</html>
