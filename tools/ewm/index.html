<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 300;
        }

        .input-group {
            margin-bottom: 25px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        input[type="text"], textarea, select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            outline: none;
        }

        input[type="text"]:focus, textarea:focus, select:focus {
            border-color: #667eea;
        }

        textarea {
            resize: vertical;
            min-height: 80px;
        }

        .size-group {
            display: flex;
            gap: 15px;
            align-items: end;
        }

        .size-group > div {
            flex: 1;
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin: 10px 5px;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        button:active {
            transform: translateY(0);
        }

        .qr-result {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }

        .qr-result.show {
            display: block;
        }

        #qrcode {
            margin: 20px 0;
            display: flex;
            justify-content: center;
        }

        .download-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .clear-btn {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }

        .error {
            color: #dc3545;
            margin-top: 10px;
            font-size: 14px;
        }

        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .size-group {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>二维码生成器</h1>
        
        <div class="input-group">
            <label for="qrText">输入内容：</label>
            <textarea id="qrText" placeholder="请输入要生成二维码的内容（网址、文本等）"></textarea>
            <div class="error" id="textError"></div>
        </div>

        <div class="size-group">
            <div class="input-group">
                <label for="qrSize">二维码大小：</label>
                <select id="qrSize">
                    <option value="200">200x200</option>
                    <option value="300" selected>300x300</option>
                    <option value="400">400x400</option>
                    <option value="500">500x500</option>
                </select>
            </div>
            
            <div class="input-group">
                <label for="errorLevel">纠错级别：</label>
                <select id="errorLevel">
                    <option value="L">低 (L)</option>
                    <option value="M" selected>中 (M)</option>
                    <option value="Q">较高 (Q)</option>
                    <option value="H">高 (H)</option>
                </select>
            </div>
        </div>

        <button onclick="generateQR()">生成二维码</button>
        <button class="clear-btn" onclick="clearAll()">清空</button>

        <div class="qr-result" id="qrResult">
            <div id="qrcode"></div>
            <button class="download-btn" onclick="downloadQR()">下载二维码</button>
        </div>
    </div>

    <script src="https://unpkg.com/qrcode-generator@1.4.4/qrcode.js"></script>
    <script>
        let currentQRCode = null;

        function generateQR() {
            const text = document.getElementById('qrText').value.trim();
            const size = parseInt(document.getElementById('qrSize').value);
            const errorLevel = document.getElementById('errorLevel').value;
            const textError = document.getElementById('textError');
            const qrResult = document.getElementById('qrResult');
            const qrcodeDiv = document.getElementById('qrcode');

            // 清除之前的错误信息
            textError.textContent = '';

            // 验证输入
            if (!text) {
                textError.textContent = '请输入要生成二维码的内容';
                qrResult.classList.remove('show');
                return;
            }

            try {
                // 创建二维码对象
                const qr = qrcode(0, errorLevel);
                qr.addData(text);
                qr.make();

                // 清除之前的二维码
                qrcodeDiv.innerHTML = '';

                // 创建二维码图像
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const modules = qr.getModuleCount();
                const cellSize = size / modules;
                
                canvas.width = size;
                canvas.height = size;

                // 设置背景色为白色
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, size, size);

                // 绘制二维码
                ctx.fillStyle = '#000000';
                for (let row = 0; row < modules; row++) {
                    for (let col = 0; col < modules; col++) {
                        if (qr.isDark(row, col)) {
                            ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                        }
                    }
                }

                // 添加到页面
                qrcodeDiv.appendChild(canvas);
                currentQRCode = canvas;
                qrResult.classList.add('show');

            } catch (error) {
                textError.textContent = '生成二维码时出错，请检查输入内容';
                qrResult.classList.remove('show');
                console.error('QR Code generation error:', error);
            }
        }

        function downloadQR() {
            if (!currentQRCode) {
                alert('请先生成二维码');
                return;
            }

            try {
                // 创建下载链接
                const link = document.createElement('a');
                link.download = 'qrcode.png';
                link.href = currentQRCode.toDataURL('image/png');
                
                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                alert('下载失败，请重试');
                console.error('Download error:', error);
            }
        }

        function clearAll() {
            document.getElementById('qrText').value = '';
            document.getElementById('qrSize').value = '300';
            document.getElementById('errorLevel').value = 'M';
            document.getElementById('textError').textContent = '';
            document.getElementById('qrResult').classList.remove('show');
            document.getElementById('qrcode').innerHTML = '';
            currentQRCode = null;
        }

        // 回车键生成二维码
        document.getElementById('qrText').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                generateQR();
            }
        });

        // 页面加载完成后聚焦到输入框
        window.addEventListener('load', function() {
            document.getElementById('qrText').focus();
        });
    </script>
</body>
</html>
