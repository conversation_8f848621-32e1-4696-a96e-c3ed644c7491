<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>照妖镜-猎户助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: url('https://www.loliapi.com/acg/pe/') center/cover no-repeat fixed;
            min-height: 100vh;
            padding: 16px;
            margin: 0;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: -1;
        }

        .top-bar {
            background: url('https://www.loliapi.com/acg/pc/') center/cover no-repeat;
            border: 3px solid #333;
            border-radius: 8px;
            padding: 20px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 16px;
            box-shadow: 4px 4px 0px #333;
            min-height: 120px;
            position: relative;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            backdrop-filter: blur(2px);
        }

        .pixel-icons {
            display: flex;
            gap: 12px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .pixel-icon {
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border: 2px solid #333;
            position: relative;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .pixel-icon.orange {
            background: #ff6b35;
        }

        .pixel-icon.gray {
            background: #999;
        }

        .pixel-icon.green {
            background: #4CAF50;
        }

        .pixel-icon.blue {
            background: #2196F3;
            position: relative;
            z-index: 2;
        }

        .pixel-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
        }

        .header-card {
            background: linear-gradient(135deg, #673AB7 0%, #512DA8 100%);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(5px);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 16px 0;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title::before {
            content: '🔮';
            font-size: 16px;
        }

        .back-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0px rgba(0,0,0,0.5);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.5);
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .function-tabs {
            display: flex;
            margin-bottom: 20px;
            border: 3px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 12px;
            background: #f0f0f0;
            border: none;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            border-right: 2px solid #333;
        }

        .tab-btn:last-child {
            border-right: none;
        }

        .tab-btn.active {
            background: #673AB7;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .input-group {
            margin-bottom: 16px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 6px;
            color: #333;
            font-weight: bold;
            font-size: 14px;
        }

        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 3px solid #333;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: all 0.1s ease;
            outline: none;
            background: #f9f9f9;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
        }

        input[type="text"]:focus, input[type="password"]:focus {
            background: white;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.2);
        }

        button {
            background: #673AB7;
            color: white;
            border: 3px solid #333;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.1s ease;
            margin: 6px 4px;
            box-shadow: 3px 3px 0px #333;
        }

        button:hover {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0px #333;
        }

        button:active {
            transform: translate(2px, 2px);
            box-shadow: 1px 1px 0px #333;
        }

        .action-btn {
            width: 100%;
        }

        .result-container {
            margin-top: 20px;
            padding: 20px;
            background: rgba(249, 249, 249, 0.95);
            border: 3px solid #333;
            border-radius: 8px;
            display: none;
            box-shadow: inset 2px 2px 0px rgba(0,0,0,0.1);
            backdrop-filter: blur(5px);
        }

        .result-container.show {
            display: block;
        }

        .result-item {
            margin-bottom: 12px;
            padding: 12px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 12px;
            word-break: break-all;
        }

        .result-label {
            font-weight: bold;
            color: #673AB7;
            margin-right: 8px;
        }

        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            margin-top: 16px;
        }

        .image-item {
            border: 3px solid #333;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 4px 4px 0px #333;
        }

        .image-item img {
            width: 100%;
            height: auto;
            display: block;
        }

        .loading {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: #666;
            animation: pulse 1.5s infinite;
            padding: 40px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .error {
            color: #f44336;
            margin-top: 10px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-align: center;
        }

        .success-msg {
            color: #4CAF50;
            font-weight: bold;
            text-align: center;
            margin-bottom: 16px;
        }

        .copy-btn {
            background: #4CAF50;
            font-size: 12px;
            padding: 6px 12px;
            margin-left: 8px;
        }

        @media (max-width: 480px) {
            .top-bar {
                margin: 0 8px 12px 8px;
                padding: 16px 12px;
                min-height: 100px;
            }
            
            .header-card {
                margin: 0 8px 12px 8px;
                padding: 16px;
            }
            
            .container {
                margin: 0 8px;
                padding: 16px;
            }
            
            .app-title {
                font-size: 16px;
            }
            
            h1 {
                font-size: 18px;
            }
            
            button {
                width: 100%;
                margin: 4px 0;
            }

            .copy-btn {
                width: auto;
                margin: 4px 0 0 0;
            }

            .image-gallery {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <div class="pixel-icons">
            <div class="pixel-icon orange"></div>
            <div class="pixel-icon gray"></div>
            <div class="pixel-icon green"></div>
        </div>
        <div class="pixel-icon blue"></div>
    </div>
    
    <div class="header-card">
        <h2 class="app-title">照妖镜-猎户助手</h2>
        <button class="back-btn" onclick="goBack()">返回首页</button>
    </div>
    
    <div class="container">
        <h1>🔮 照妖镜</h1>
        
        <div class="function-tabs">
            <button class="tab-btn active" onclick="switchTab('create')">创建空间</button>
            <button class="tab-btn" onclick="switchTab('view')">查看图片</button>
        </div>

        <!-- 创建临时空间 -->
        <div id="createTab" class="tab-content active">
            <div class="input-group">
                <label for="createTokenInput">卡密(Token)：</label>
                <input type="password" id="createTokenInput" placeholder="请输入您的Token">
                <div class="error" id="createTokenError"></div>
            </div>

            <button class="action-btn" onclick="createSpace()">🚀 创建临时空间</button>
        </div>

        <!-- 查看拍摄图片 -->
        <div id="viewTab" class="tab-content">
            <div class="input-group">
                <label for="viewTokenInput">卡密(Token)：</label>
                <input type="password" id="viewTokenInput" placeholder="请输入您的Token">
                <div class="error" id="viewTokenError"></div>
            </div>

            <div class="input-group">
                <label for="passwordInput">查看密码：</label>
                <input type="text" id="passwordInput" placeholder="请输入查看密码">
                <div class="error" id="passwordError"></div>
            </div>

            <button class="action-btn" onclick="viewImages()">👁️ 查看拍摄图片</button>
        </div>

        <div class="result-container" id="resultContainer">
            <div id="loadingMsg" class="loading" style="display: none;">处理中...</div>
            <div id="resultContent"></div>
        </div>
        
        <div class="error" id="errorMsg"></div>
    </div>

    <script>
        let isProcessing = false;

        // 切换标签页
        function switchTab(tab) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(tab + 'Tab').classList.add('active');
            
            // 清除结果
            clearResults();
        }

        // 清除结果
        function clearResults() {
            document.getElementById('resultContainer').classList.remove('show');
            document.getElementById('errorMsg').textContent = '';
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('已复制到剪贴板');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('已复制到剪贴板');
            });
        }

        // 创建临时空间
        async function createSpace() {
            if (isProcessing) return;
            
            const tokenInput = document.getElementById('createTokenInput');
            const tokenError = document.getElementById('createTokenError');
            const errorMsg = document.getElementById('errorMsg');
            const resultContainer = document.getElementById('resultContainer');
            const loadingMsg = document.getElementById('loadingMsg');
            const resultContent = document.getElementById('resultContent');
            
            // 清除错误信息
            tokenError.textContent = '';
            errorMsg.textContent = '';
            
            const token = tokenInput.value.trim();
            
            if (!token) {
                tokenError.textContent = '请输入Token';
                return;
            }
            
            isProcessing = true;
            loadingMsg.style.display = 'block';
            loadingMsg.textContent = '正在创建临时空间...';
            resultContent.innerHTML = '';
            resultContainer.classList.add('show');
            
            try {
                const url = `https://api.qnm6.top/api/zyj/?token=${encodeURIComponent(token)}&action=create`;
                const response = await fetch(url);
                const data = await response.json();
                
                loadingMsg.style.display = 'none';
                
                if (data.code === 200 || data.code === '200') {
                    resultContent.innerHTML = `
                        <div class="success-msg">✅ ${data.message || '临时空间创建成功'}</div>
                        <div class="result-item">
                            <span class="result-label">访问链接:</span>${data.tpurl || '未返回'}
                            <button class="copy-btn" onclick="copyToClipboard('${data.tpurl || ''}')">复制</button>
                        </div>
                        <div class="result-item">
                            <span class="result-label">查看密码:</span>${data.pw || '未返回'}
                            <button class="copy-btn" onclick="copyToClipboard('${data.pw || ''}')">复制</button>
                        </div>
                    `;
                } else {
                    throw new Error(data.message || '创建失败');
                }
                
            } catch (error) {
                loadingMsg.style.display = 'none';
                resultContainer.classList.remove('show');
                errorMsg.textContent = '创建失败: ' + error.message;
            }
            
            isProcessing = false;
        }

        // 查看拍摄图片
        async function viewImages() {
            if (isProcessing) return;
            
            const tokenInput = document.getElementById('viewTokenInput');
            const passwordInput = document.getElementById('passwordInput');
            const tokenError = document.getElementById('viewTokenError');
            const passwordError = document.getElementById('passwordError');
            const errorMsg = document.getElementById('errorMsg');
            const resultContainer = document.getElementById('resultContainer');
            const loadingMsg = document.getElementById('loadingMsg');
            const resultContent = document.getElementById('resultContent');
            
            // 清除错误信息
            tokenError.textContent = '';
            passwordError.textContent = '';
            errorMsg.textContent = '';
            
            const token = tokenInput.value.trim();
            const password = passwordInput.value.trim();
            
            let hasError = false;
            if (!token) {
                tokenError.textContent = '请输入Token';
                hasError = true;
            }
            if (!password) {
                passwordError.textContent = '请输入查看密码';
                hasError = true;
            }
            
            if (hasError) return;
            
            isProcessing = true;
            loadingMsg.style.display = 'block';
            loadingMsg.textContent = '正在获取拍摄图片...';
            resultContent.innerHTML = '';
            resultContainer.classList.add('show');
            
            try {
                const url = `https://api.qnm6.top/api/zyj/?token=${encodeURIComponent(token)}&pw=${encodeURIComponent(password)}&action=view`;
                const response = await fetch(url);
                const data = await response.json();
                
                loadingMsg.style.display = 'none';
                
                if (data.code === 200 || data.code === '200') {
                    let imagesHtml = '';
                    if (data.imgurl && data.imgurl.length > 0) {
                        imagesHtml = '<div class="image-gallery">';
                        data.imgurl.forEach((url, index) => {
                            imagesHtml += `
                                <div class="image-item">
                                    <img src="${url}" alt="拍摄图片${index + 1}" onerror="this.parentElement.style.display='none'">
                                </div>
                            `;
                        });
                        imagesHtml += '</div>';
                    }
                    
                    resultContent.innerHTML = `
                        <div class="success-msg">✅ ${data.message || '获取成功'}</div>
                        <div class="result-item">
                            <span class="result-label">图片数量:</span>${data.count || 0} 张
                        </div>
                        ${imagesHtml}
                    `;
                } else {
                    throw new Error(data.message || '获取失败');
                }
                
            } catch (error) {
                loadingMsg.style.display = 'none';
                resultContainer.classList.remove('show');
                errorMsg.textContent = '获取失败: ' + error.message;
            }
            
            isProcessing = false;
        }

        // 返回根目录
        function goBack() {
            window.location.href = '/';
        }

        // 页面加载完成后聚焦到第一个输入框
        window.addEventListener('load', function() {
            document.getElementById('createTokenInput').focus();
        });
    </script>
</body>
</html>
