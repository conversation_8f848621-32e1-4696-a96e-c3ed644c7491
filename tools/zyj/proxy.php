<?php
// CORS代理文件
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 获取参数
$token = $_GET['token'] ?? '';
$action = $_GET['action'] ?? '';
$pw = $_GET['pw'] ?? '';

if (empty($token) || empty($action)) {
    echo json_encode(['code' => 400, 'message' => '参数不完整']);
    exit();
}

// 构建API URL
$api_url = "https://api.qnm6.top/api/zyj?token=" . urlencode($token) . "&action=" . urlencode($action);
if (!empty($pw)) {
    $api_url .= "&pw=" . urlencode($pw);
}

// 发起请求
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 30,
        'header' => [
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]
]);

$response = @file_get_contents($api_url, false, $context);

if ($response === false) {
    echo json_encode(['code' => 500, 'message' => 'API请求失败']);
    exit();
}

// 返回结果
echo $response;
?>
