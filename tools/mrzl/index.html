<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每日自律-猎户助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: url('https://www.loliapi.com/acg/pe/') center/cover no-repeat fixed;
            min-height: 100vh;
            padding: 16px;
            margin: 0;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: -1;
        }

        .top-bar {
            background: url('https://www.loliapi.com/acg/pc/') center/cover no-repeat;
            border: 3px solid #333;
            border-radius: 8px;
            padding: 20px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 16px;
            box-shadow: 4px 4px 0px #333;
            min-height: 120px;
            position: relative;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            backdrop-filter: blur(2px);
        }

        .pixel-icons {
            display: flex;
            gap: 12px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .pixel-icon {
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border: 2px solid #333;
            position: relative;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .pixel-icon.orange {
            background: #ff6b35;
        }

        .pixel-icon.gray {
            background: #999;
        }

        .pixel-icon.green {
            background: #4CAF50;
        }

        .pixel-icon.blue {
            background: #2196F3;
            position: relative;
            z-index: 2;
        }

        .pixel-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
        }

        .header-card {
            background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(5px);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 16px 0;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title::before {
            content: '💪';
            font-size: 16px;
        }

        .back-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0px rgba(0,0,0,0.5);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.5);
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .zilv-container {
            margin: 20px 0;
            border: 3px solid #333;
            border-radius: 8px;
            background: rgba(249, 249, 249, 0.95);
            min-height: 200px;
            position: relative;
            overflow: hidden;
            padding: 20px;
        }

        .zilv-loading {
            font-size: 16px;
            font-weight: bold;
            color: #666;
            animation: pulse 1.5s infinite;
            text-align: center;
            padding: 40px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .video-list {
            display: none;
            width: 100%;
        }

        .video-list.show {
            display: block;
        }

        .video-item {
            margin-bottom: 12px;
            padding: 12px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.1s ease;
        }

        .video-item:hover {
            background: #f0f0f0;
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.1);
        }

        .video-name {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .video-url {
            font-size: 12px;
            color: #666;
            word-break: break-all;
        }

        .video-player {
            display: none;
            width: 100%;
            margin-top: 16px;
        }

        .video-player.show {
            display: block;
        }

        .video-player video {
            width: 100%;
            max-height: 300px;
            border: 3px solid #333;
            border-radius: 8px;
            background: #000;
        }

        .video-controls {
            margin-top: 12px;
            text-align: center;
        }

        .video-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 12px;
            text-align: center;
        }

        .zilv-btn {
            background: #9C27B0;
            color: white;
            border: 3px solid #333;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.1s ease;
            margin: 6px 4px;
            box-shadow: 3px 3px 0px #333;
            width: 100%;
        }

        .zilv-btn:hover {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0px #333;
        }

        .zilv-btn:active {
            transform: translate(2px, 2px);
            box-shadow: 1px 1px 0px #333;
        }

        .back-to-list-btn {
            background: #4CAF50;
            margin-bottom: 12px;
        }

        .error {
            color: #f44336;
            margin-top: 10px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-align: center;
        }

        @media (max-width: 480px) {
            .top-bar {
                margin: 0 8px 12px 8px;
                padding: 16px 12px;
                min-height: 100px;
            }
            
            .header-card {
                margin: 0 8px 12px 8px;
                padding: 16px;
            }
            
            .container {
                margin: 0 8px;
                padding: 16px;
            }
            
            .app-title {
                font-size: 16px;
            }
            
            h1 {
                font-size: 18px;
            }
            
            .zilv-container {
                min-height: 180px;
                padding: 16px;
            }

            .video-item {
                padding: 10px;
                margin-bottom: 10px;
            }

            .video-name {
                font-size: 13px;
            }

            .video-url {
                font-size: 11px;
            }

            .video-player video {
                max-height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <div class="pixel-icons">
            <div class="pixel-icon orange"></div>
            <div class="pixel-icon gray"></div>
            <div class="pixel-icon green"></div>
        </div>
        <div class="pixel-icon blue"></div>
    </div>
    
    <div class="header-card">
        <h2 class="app-title">每日自律-猎户助手</h2>
        <button class="back-btn" onclick="goBack()">返回首页</button>
    </div>
    
    <div class="container">
        <h1>💪 每日自律</h1>

        <div class="zilv-container" id="zilvContainer">
            <div class="zilv-loading" id="zilvLoading">正在加载视频列表...</div>

            <div class="video-list" id="videoList">
                <!-- 视频列表将在这里动态生成 -->
            </div>

            <div class="video-player" id="videoPlayer">
                <div class="video-title" id="videoTitle"></div>
                <video id="videoElement" controls preload="metadata">
                    您的浏览器不支持视频播放
                </video>
                <div class="video-controls">
                    <button class="zilv-btn back-to-list-btn" onclick="backToList()">📋 返回列表</button>
                </div>
            </div>
        </div>

        <button class="zilv-btn" onclick="refreshVideoList()" id="refreshBtn">🔄 刷新视频列表</button>

        <div class="error" id="errorMsg"></div>
    </div>

    <script>
        let isLoading = false;
        let videoData = [];

        // 加载视频列表
        async function loadVideoList() {
            if (isLoading) return;

            isLoading = true;
            const loading = document.getElementById('zilvLoading');
            const videoList = document.getElementById('videoList');
            const errorMsg = document.getElementById('errorMsg');

            // 显示加载状态
            loading.style.display = 'block';
            videoList.classList.remove('show');
            errorMsg.textContent = '';

            try {
                const response = await fetch('./zilv.json');

                if (!response.ok) {
                    throw new Error('网络请求失败');
                }

                const data = await response.json();

                if (Array.isArray(data) && data.length > 0) {
                    videoData = data;
                    renderVideoList(data);

                    loading.style.display = 'none';
                    videoList.classList.add('show');
                } else {
                    throw new Error('视频列表为空或格式错误');
                }

            } catch (error) {
                loading.style.display = 'none';
                errorMsg.textContent = '加载失败: ' + error.message;
                console.error('加载视频列表失败:', error);
            }

            isLoading = false;
        }

        // 渲染视频列表
        function renderVideoList(videos) {
            const videoList = document.getElementById('videoList');
            videoList.innerHTML = '';

            videos.forEach((video, index) => {
                const videoItem = document.createElement('div');
                videoItem.className = 'video-item';
                videoItem.onclick = () => playVideo(video, index);

                videoItem.innerHTML = `
                    <div class="video-name">📺 ${video.name || '未命名视频'}</div>
                    <div class="video-url">${video.m3u8 || '无链接'}</div>
                `;

                videoList.appendChild(videoItem);
            });
        }

        // 播放视频
        function playVideo(video, index) {
            const videoList = document.getElementById('videoList');
            const videoPlayer = document.getElementById('videoPlayer');
            const videoTitle = document.getElementById('videoTitle');
            const videoElement = document.getElementById('videoElement');
            const refreshBtn = document.getElementById('refreshBtn');

            // 隐藏列表，显示播放器
            videoList.classList.remove('show');
            videoPlayer.classList.add('show');
            refreshBtn.style.display = 'none';

            // 设置视频信息
            videoTitle.textContent = video.name || '未命名视频';
            videoElement.src = video.m3u8;

            // 尝试播放
            videoElement.load();
        }

        // 返回视频列表
        function backToList() {
            const videoList = document.getElementById('videoList');
            const videoPlayer = document.getElementById('videoPlayer');
            const videoElement = document.getElementById('videoElement');
            const refreshBtn = document.getElementById('refreshBtn');

            // 停止播放
            videoElement.pause();
            videoElement.src = '';

            // 显示列表，隐藏播放器
            videoPlayer.classList.remove('show');
            videoList.classList.add('show');
            refreshBtn.style.display = 'block';
        }

        // 刷新视频列表
        function refreshVideoList() {
            backToList();
            loadVideoList();
        }

        // 返回根目录
        function goBack() {
            window.location.href = '/';
        }

        // 页面加载完成后自动加载视频列表
        window.addEventListener('load', function() {
            loadVideoList();
        });
    </script>
</body>
</html>
