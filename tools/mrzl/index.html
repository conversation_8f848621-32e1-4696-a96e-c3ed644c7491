<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每日自律-猎户助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: url('https://www.loliapi.com/acg/pe/') center/cover no-repeat fixed;
            min-height: 100vh;
            padding: 16px;
            margin: 0;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            z-index: -1;
        }

        .top-bar {
            background: url('https://www.loliapi.com/acg/pc/') center/cover no-repeat;
            border: 3px solid #333;
            border-radius: 8px;
            padding: 20px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 16px;
            box-shadow: 4px 4px 0px #333;
            min-height: 120px;
            position: relative;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            backdrop-filter: blur(2px);
        }

        .pixel-icons {
            display: flex;
            gap: 12px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .pixel-icon {
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border: 2px solid #333;
            position: relative;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .pixel-icon.orange {
            background: #ff6b35;
        }

        .pixel-icon.gray {
            background: #999;
        }

        .pixel-icon.green {
            background: #4CAF50;
        }

        .pixel-icon.blue {
            background: #2196F3;
            position: relative;
            z-index: 2;
        }

        .pixel-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
        }

        .header-card {
            background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 16px;
            max-width: 480px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(5px);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 16px 0;
            text-shadow: 2px 2px 0px rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-title::before {
            content: '💪';
            font-size: 16px;
        }

        .back-btn {
            background: rgba(0,0,0,0.3);
            color: white;
            border: 3px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.1s ease;
            text-shadow: 1px 1px 0px rgba(0,0,0,0.5);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.5);
            transform: translate(1px, 1px);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border: 4px solid #333;
            border-radius: 16px;
            padding: 24px;
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            box-shadow: 6px 6px 0px #333;
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
        }

        .zilv-container {
            margin: 20px 0;
            border: 3px solid #333;
            border-radius: 8px;
            background: rgba(249, 249, 249, 0.95);
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            padding: 20px;
        }

        .zilv-loading {
            font-size: 16px;
            font-weight: bold;
            color: #666;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .zilv-content {
            display: none;
            text-align: center;
            width: 100%;
        }

        .zilv-content.show {
            display: block;
        }

        .zilv-text {
            font-size: 16px;
            line-height: 1.6;
            color: #333;
            margin-bottom: 16px;
            padding: 16px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 2px 2px 0px rgba(0,0,0,0.1);
        }

        .zilv-author {
            font-size: 14px;
            color: #666;
            font-style: italic;
            margin-top: 12px;
        }

        .zilv-btn {
            background: #9C27B0;
            color: white;
            border: 3px solid #333;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.1s ease;
            margin: 12px 4px;
            box-shadow: 3px 3px 0px #333;
            width: 100%;
        }

        .zilv-btn:hover {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0px #333;
        }

        .zilv-btn:active {
            transform: translate(2px, 2px);
            box-shadow: 1px 1px 0px #333;
        }

        .error {
            color: #f44336;
            margin-top: 10px;
            font-size: 12px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-align: center;
        }

        @media (max-width: 480px) {
            .top-bar {
                margin: 0 8px 12px 8px;
                padding: 16px 12px;
                min-height: 100px;
            }
            
            .header-card {
                margin: 0 8px 12px 8px;
                padding: 16px;
            }
            
            .container {
                margin: 0 8px;
                padding: 16px;
            }
            
            .app-title {
                font-size: 16px;
            }
            
            h1 {
                font-size: 18px;
            }
            
            .zilv-container {
                min-height: 180px;
                padding: 16px;
            }

            .zilv-text {
                font-size: 14px;
                padding: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <div class="pixel-icons">
            <div class="pixel-icon orange"></div>
            <div class="pixel-icon gray"></div>
            <div class="pixel-icon green"></div>
        </div>
        <div class="pixel-icon blue"></div>
    </div>
    
    <div class="header-card">
        <h2 class="app-title">每日自律-猎户助手</h2>
        <button class="back-btn" onclick="goBack()">返回首页</button>
    </div>
    
    <div class="container">
        <h1>💪 每日自律</h1>
        
        <div class="zilv-container" id="zilvContainer">
            <div class="zilv-loading" id="zilvLoading">正在加载自律内容...</div>
            <div class="zilv-content" id="zilvContent">
                <div class="zilv-text" id="zilvText"></div>
                <div class="zilv-author" id="zilvAuthor"></div>
            </div>
        </div>
        
        <button class="zilv-btn" onclick="refreshZilv()">🔄 刷新自律内容</button>
        
        <div class="error" id="errorMsg"></div>
    </div>

    <script>
        let isLoading = false;

        // 加载自律内容
        async function loadZilvContent() {
            if (isLoading) return;
            
            isLoading = true;
            const loading = document.getElementById('zilvLoading');
            const content = document.getElementById('zilvContent');
            const errorMsg = document.getElementById('errorMsg');
            const zilvText = document.getElementById('zilvText');
            const zilvAuthor = document.getElementById('zilvAuthor');
            
            // 显示加载状态
            loading.style.display = 'block';
            content.classList.remove('show');
            errorMsg.textContent = '';
            
            try {
                const response = await fetch('http://127.0.0.1:8888/zilv.json');
                
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                
                const data = await response.json();
                
                // 显示内容
                if (data.text || data.content) {
                    zilvText.textContent = data.text || data.content || '暂无内容';
                    zilvAuthor.textContent = data.author ? `—— ${data.author}` : '';
                    
                    loading.style.display = 'none';
                    content.classList.add('show');
                } else {
                    throw new Error('数据格式错误');
                }
                
            } catch (error) {
                loading.style.display = 'none';
                errorMsg.textContent = '加载失败: ' + error.message;
                console.error('加载自律内容失败:', error);
            }
            
            isLoading = false;
        }

        // 刷新自律内容
        function refreshZilv() {
            loadZilvContent();
        }

        // 返回根目录
        function goBack() {
            window.location.href = '/';
        }

        // 页面加载完成后自动加载第一条内容
        window.addEventListener('load', function() {
            loadZilvContent();
        });
    </script>
</body>
</html>
